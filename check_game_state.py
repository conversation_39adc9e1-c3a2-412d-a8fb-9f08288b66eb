#!/usr/bin/env python
"""
Script to check the current game state and debug stage progression issues.
"""
import os
import django
import sys

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'prompt_game.settings')
django.setup()

# Import models
from game.models import GameSession
from game.role_progression import ROLE_PROGRESSION

def check_game_state():
    """Check the current game state and debug progression issues"""
    print("Checking game state...")
    
    # Get the most recent game session
    session = GameSession.objects.last()
    if not session:
        print("No game sessions found")
        return
    
    print(f"Current role: {session.current_role}")
    print(f"Role challenges completed: {session.role_challenges_completed}")
    print(f"Total challenges completed: {session.challenges_completed}")
    print(f"Current task: {session.current_task}")
    print(f"Current manager: {session.current_manager}")
    print(f"Completed roles: {session.get_completed_roles()}")
    print(f"Game completed: {session.game_completed}")
    
    # Check what's required for current role
    role_info = ROLE_PROGRESSION.get(session.current_role, {})
    challenges_required = role_info.get("challenges_required", 3)
    next_role = role_info.get("next_role")
    print(f"\nChallenges required for {session.current_role}: {challenges_required}")
    print(f"Next role: {next_role}")
    
    # Check if promotion should happen
    if session.role_challenges_completed >= challenges_required:
        print("\n*** PROMOTION SHOULD HAPPEN! ***")
        if next_role:
            print(f"Player should be promoted to: {next_role}")
        else:
            print("No next role - game should be completed")
    else:
        needed = challenges_required - session.role_challenges_completed
        print(f"\nNeed {needed} more challenges for promotion")
    
    # Check recent messages
    print(f"\nRecent messages (last 5):")
    recent_messages = session.messages.all().order_by('-timestamp')[:5]
    for msg in recent_messages:
        print(f"  {msg.sender}: {msg.text[:100]}...")

    # Check task progression for current role
    from game.all_role_tasks import get_all_role_tasks
    current_role_tasks = get_all_role_tasks(session.current_role)
    print(f"\nTasks for {session.current_role}:")
    for i, task in enumerate(current_role_tasks):
        status = "CURRENT" if task["id"] == session.current_task else "PENDING"
        if i < session.role_challenges_completed:
            status = "COMPLETED"
        print(f"  {i+1}. {task['id']} - {status}")

    # Check if there's a task ordering issue
    current_task_index = -1
    for i, task in enumerate(current_role_tasks):
        if task["id"] == session.current_task:
            current_task_index = i
            break

    if current_task_index != session.role_challenges_completed:
        print(f"\n*** TASK ORDERING ISSUE DETECTED ***")
        print(f"Current task index: {current_task_index}")
        print(f"Role challenges completed: {session.role_challenges_completed}")
        print(f"Expected current task: {current_role_tasks[session.role_challenges_completed]['id'] if session.role_challenges_completed < len(current_role_tasks) else 'None'}")
    else:
        print(f"\nTask progression is correct")

if __name__ == "__main__":
    check_game_state()
